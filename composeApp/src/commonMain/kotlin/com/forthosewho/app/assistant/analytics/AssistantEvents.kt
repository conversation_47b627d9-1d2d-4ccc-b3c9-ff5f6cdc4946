package com.forthosewho.app.assistant.analytics

import com.forthosewho.app.analytics.AnalyticsEvent

const val TRIGGER_SOURCE = "trigger_source"
const val NUM_USER_REPLIES = "num_user_replies"
const val CLUES_TO_BE_ADDED = "clues_to_be_added_count"
const val CLUES_TO_BE_REMOVED = "clues_to_be_removed_count"
const val CLUES_TO_REMAIN = "clues_to_remain_count"
const val NUM_CLUES_GENERATED = "num_clues_generated"
const val NUM_CLUES_ACCEPTED = "num_clues_accepted"
const val NUM_CLUES_REJECTED = "num_clues_rejected"

sealed class AssistantEvents {

    data class AssistantOpened(override val properties: Map<String, String>? = null) : AnalyticsEvent {
        override val eventName: String = "Assistant Opened"
    }

    data class OnboardingClues(override val properties: Map<String, String>? = null) : AnalyticsEvent {
        override val eventName: String = "Onboarding Clues"
    }

    data class OnboardingCluesDiscarded(override val properties: Map<String, String>? = null) : AnalyticsEvent {
        override val eventName: String = "Onboarding Clues Discarded"
    }

    data class CompleteProfileClicked(override val properties: Map<String, String>? = null) : AnalyticsEvent {
        override val eventName: String = "Complete Profile Clicked"
    }

    data class VerifyClues(override val properties: Map<String, String>? = null) : AnalyticsEvent {
        override val eventName: String = "Verify Clues"
    }

    data class CluesDiscarded(override val properties: Map<String, String>? = null) : AnalyticsEvent {
        override val eventName: String = "Clues Discarded"
    }

    data class AssistantXPressed(override val properties: Map<String, String>? = null) : AnalyticsEvent {
        override val eventName: String = "Assistant X Pressed"
    }

    data class NoUpdatesRightNow(override val properties: Map<String, String>? = null) : AnalyticsEvent {
        override val eventName: String = "No Updates Right Now"
    }

    data class ClueAccepted(override val properties: Map<String, String>? = null) : AnalyticsEvent {
        override val eventName: String = "Clue Accepted"
    }

    data class ClueRejected(override val properties: Map<String, String>? = null) : AnalyticsEvent {
        override val eventName: String = "Clue Rejected"
    }
}