package com.forthosewho.app.assistant.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.forthosewho.app.account.domain.model.Clue
import com.forthosewho.app.analytics.domain.usecase.AnalyticsUseCase
import com.forthosewho.app.assistant.analytics.AssistantEvents
import com.forthosewho.app.assistant.analytics.AssistantEvents.CluesGenerated
import com.forthosewho.app.assistant.analytics.AssistantEvents.NoUpdatesRightNow
import com.forthosewho.app.assistant.analytics.CLUES_TO_BE_ADDED
import com.forthosewho.app.assistant.analytics.CLUES_TO_BE_REMOVED
import com.forthosewho.app.assistant.analytics.CLUES_TO_REMAIN
import com.forthosewho.app.assistant.analytics.NUM_CLUES_REJECTED
import com.forthosewho.app.assistant.analytics.NUM_CLUES_TO_BE_ADDED
import com.forthosewho.app.assistant.analytics.NUM_CLUES_TO_BE_REMOVED
import com.forthosewho.app.assistant.analytics.NUM_CLUES_TO_REMAIN
import com.forthosewho.app.assistant.analytics.NUM_USER_REPLIES
import com.forthosewho.app.assistant.analytics.TRIGGER_SOURCE
import com.forthosewho.app.assistant.domain.usecase.InitializeThreadUseCase
import com.forthosewho.app.assistant.domain.usecase.RetrieveCluesUseCase
import com.forthosewho.app.assistant.domain.usecase.SendMessageUseCase
import com.forthosewho.app.assistant.domain.usecase.UserCluesUseCase
import com.forthosewho.app.assistant.ui.model.AssistantMessage
import com.forthosewho.app.assistant.ui.model.AssistantMessage.Companion.createAssistantMessage
import com.forthosewho.app.assistant.ui.model.AssistantState
import com.forthosewho.app.assistant.ui.model.CluesContainer
import com.forthosewho.app.assistant.ui.model.CluesContainer.Companion.createCluesContainer
import com.forthosewho.app.assistant.ui.model.InitializeThreadCategory
import com.forthosewho.app.login.domain.model.Me
import com.forthosewho.app.login.domain.usecase.MeUseCase
import com.github.michaelbull.result.onFailure
import com.github.michaelbull.result.onSuccess
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch

class AssistantViewModel(
    private val initializeThreadUseCase: InitializeThreadUseCase,
    private val meUseCase: MeUseCase,
    private val sendMessageUseCase: SendMessageUseCase,
    private val userCluesUseCase: UserCluesUseCase,
    private val retrieveCluesUseCase: RetrieveCluesUseCase,
    private val analyticsUseCase: AnalyticsUseCase
) : ViewModel() {

    private val _state = MutableStateFlow(AssistantState.State())
    val state = _state.asStateFlow()

    fun initializeDiscussion(category: InitializeThreadCategory, itemId: String? = null) {
        analyticsUseCase.trackEvent(
            AssistantEvents.AssistantOpened(properties = mapOf(TRIGGER_SOURCE to category.category))
        )
        _state.update { state ->
            state.copy(
                screenState = AssistantState.ScreenState.StateInitial,
                isOnboarding = category == InitializeThreadCategory.ONBOARDING,
                category = category,
                clueId = itemId,
                messages = listOf(AssistantMessage.createLoadingAssistantMessage()),
                container = CluesContainer()
            )
        }
        viewModelScope.launch {
            initializeThreadUseCase.invoke(
                InitializeThreadUseCase.Params(
                    category = _state.value.category.category,
                    itemId = _state.value.clueId
                )
            ).onSuccess { result ->
                _state.update { state ->
                    state.copy(
                        screenState = AssistantState.ScreenState.StateDiscussion,
                        threadId = result.threadId,
                        messages = result.messageItems.map {
                            createAssistantMessage(
                                id = it.id,
                                assistantMessage = it.message.messageString
                            )
                        }
                    )
                }
            }.onFailure { error ->
                _state.update { state ->
                    state.copy(
                        screenState = AssistantState.ScreenState.StateDiscussion,
                        messages = state.messages.filterNot { it.isLoading }
                    )
                }
            }
        }
    }

    fun sendEvent(answer: String? = null, continueDiscussion: Boolean = false) {
        answer?.let {
            _state.update { state ->
                state.copy(
                    screenState = AssistantState.ScreenState.StateDiscussion,
                    messages = state.messages.plus(AssistantMessage.createUserAnswerMessage(answer = answer))
                )
            }

            _state.update { state ->
                state.copy(
                    screenState = AssistantState.ScreenState.StateDiscussion,
                    messages = state.messages.plus(AssistantMessage.createLoadingAssistantMessage())
                )
            }
        }
        viewModelScope.launch {
            sendMessageUseCase.invoke(
                SendMessageUseCase.Params(
                    threadId = _state.value.threadId,
                    message = answer,
                    continueDiscussion = continueDiscussion
                )
            ).onSuccess { result ->
                if (result.message.hasCluesToChange()) {
                    if (_state.value.isOnboarding) {
                        _state.update { state ->
                            state.copy(
                                screenState = AssistantState.ScreenState.StateCluesOnboardingVerifyMode,
                                container = createCluesContainer(result.message)
                            )
                        }
                        analyticsUseCase.trackEvent(
                            AssistantEvents.OnboardingClues(
                                properties = mapOf(
                                    CLUES_TO_BE_ADDED to _state.value.container.cluesToBeAdded.count().toString()
                                )
                            )
                        )
                    } else {
                        analyticsUseCase.trackEvent(
                            CluesGenerated(
                                properties = mapOf(
                                    NUM_CLUES_TO_BE_ADDED to result.message.clueToBeAddedCount().toString(),
                                    NUM_CLUES_TO_BE_REMOVED to result.message.clueToBeRemovedCount().toString(),
                                    NUM_CLUES_TO_REMAIN to result.message.clueToBeRemainCount().toString()
                                )
                            )
                        )
                        _state.update { state ->
                            state.copy(
                                screenState = AssistantState.ScreenState.StateCluesVerifyMode,
                                container = createCluesContainer(result.message)
                            )
                        }
                    }
                } else {
                    val message = createAssistantMessage(id = result.id, assistantMessage = result.message.messageString)
                    _state.update { state ->
                        state.copy(
                            screenState = AssistantState.ScreenState.StateDiscussion,
                            messages = state.messages.filterNot { it.isLoading } + message
                        )
                    }
                }

            }.onFailure { error ->
                _state.update { state ->
                    state.copy(
                        screenState = AssistantState.ScreenState.StateDiscussion,
                        messages = state.messages.filterNot { it.isLoading }
                    )
                }
            }
        }
    }

    fun verifyClues() {
        viewModelScope.launch {
            userCluesUseCase.invoke(UserCluesUseCase.Params(cluesContainer = _state.value.container)).onSuccess {
                if (_state.value.isOnboarding) {
                    analyticsUseCase.trackEvent(
                        AssistantEvents.CompleteProfileClicked(
                            properties = mapOf(
                                CLUES_TO_BE_ADDED to _state.value.container.cluesToBeAdded.count().toString()
                            )
                        )
                    )
                } else {
                    analyticsUseCase.trackEvent(
                        AssistantEvents.ClueAccepted(
                            properties = mapOf(
                                CLUES_TO_BE_ADDED to _state.value.container.cluesToBeAdded.count().toString(),
                                CLUES_TO_BE_REMOVED to _state.value.container.cluesToBeRemoved.count().toString(),
                                CLUES_TO_REMAIN to _state.value.container.cluesToRemain.count().toString()
                            )
                        )
                    )
                }
                meUseCase.invoke().onSuccess { me ->
                    when (me.status) {
                        Me.Status.ONBOARDED -> {
                            _state.update { state ->
                                state.copy(
                                    screenState = AssistantState.ScreenState.StateDiscussionEnd,
                                    container = _state.value.container
                                )
                            }
                        }

                        else -> {
                            _state.update { state ->
                                state.copy(
                                    screenState = AssistantState.ScreenState.StateDiscussion,
                                    container = _state.value.container
                                )
                            }
                        }
                    }
                }.onFailure {
                    _state.update { state -> state.copy(screenState = AssistantState.ScreenState.StateDiscussion) }
                }
            }.onFailure {
                _state.update { state -> state.copy(screenState = AssistantState.ScreenState.StateDiscussion) }
            }
        }
    }

    fun retrieveClues() {
        analyticsUseCase.trackEvent(
            AssistantEvents.AssistantXPressed(
                properties = mapOf(NUM_USER_REPLIES to _state.value.messages.count { it.isUser }.toString())
            )
        )
        _state.update { state -> state.copy(screenState = AssistantState.ScreenState.StateCluesBlockingState) }
        viewModelScope.launch {
            retrieveCluesUseCase.invoke(
                param = RetrieveCluesUseCase.Params(
                    threadId = _state.value.threadId,
                    category = _state.value.category.category
                )
            ).onSuccess { result ->
                if (result.message.hasCluesToChange()) {
                    if (_state.value.isOnboarding) {
                        analyticsUseCase.trackEvent(
                            CluesGenerated(
                                properties = mapOf(
                                    NUM_CLUES_TO_BE_ADDED to result.message.clueToBeAddedCount().toString(),
                                    NUM_CLUES_TO_BE_REMOVED to result.message.clueToBeRemovedCount().toString(),
                                    NUM_CLUES_TO_REMAIN to result.message.clueToBeRemainCount().toString()
                                )
                            )
                        )
                        _state.update { state ->
                            state.copy(
                                screenState = AssistantState.ScreenState.StateCluesOnboardingVerifyMode,
                                container = createCluesContainer(result.message)
                            )
                        }
                    } else {
                        analyticsUseCase.trackEvent(
                            CluesGenerated(
                                properties = mapOf(
                                    NUM_CLUES_TO_BE_ADDED to result.message.clueToBeAddedCount().toString(),
                                    NUM_CLUES_TO_BE_REMOVED to result.message.clueToBeRemovedCount().toString(),
                                    NUM_CLUES_TO_REMAIN to result.message.clueToBeRemainCount().toString()
                                )
                            )
                        )
                        _state.update { state ->
                            state.copy(
                                screenState = AssistantState.ScreenState.StateCluesVerifyMode,
                                container = createCluesContainer(result.message)
                            )
                        }
                    }
                } else {
                    analyticsUseCase.trackEvent(NoUpdatesRightNow())
                    _state.update { state ->
                        state.copy(screenState = AssistantState.ScreenState.StateCluesNoUpdate)
                    }
                }
            }.onFailure {
                analyticsUseCase.trackEvent(NoUpdatesRightNow())
                _state.update { state ->
                    state.copy(screenState = AssistantState.ScreenState.StateCluesNoUpdate)
                }
            }
        }
    }

    fun exitAssistant() {
        if (_state.value.isOnboarding) {
            analyticsUseCase.trackEvent(
                AssistantEvents.OnboardingCluesDiscarded(
                    properties = mapOf(
                        NUM_CLUES_REJECTED to _state.value.container.cluesToBeAdded.count().toString(),
                    )
                )
            )
        } else {
            analyticsUseCase.trackEvent(
                AssistantEvents.CluesDiscarded(
                    properties = mapOf(
                        NUM_CLUES_REJECTED to _state.value.container.cluesToBeAdded.count { !it.enabled }.toString(),
                    )
                )
            )
        }
        _state.update { state ->
            state.copy(
                screenState = AssistantState.ScreenState.StateInitial,
                messages = listOf(AssistantMessage.createLoadingAssistantMessage()),
                container = CluesContainer()
            )
        }
    }

    fun backToDiscussion() {
        _state.update { state -> state.copy(screenState = AssistantState.ScreenState.StateInitial) }
        sendEvent(continueDiscussion = true)
    }

    fun onClueToBeRemovedClicked(clue: Clue) {
        val listToBeRemoved = _state.value.container.cluesToBeRemoved.map { existingClue ->
            if (existingClue == clue) {
                existingClue.copy(enabled = !clue.enabled)
            } else {
                existingClue
            }
        }
        _state.update { state ->
            state.copy(container = _state.value.container.copy(cluesToBeRemoved = listToBeRemoved))
        }
    }

    fun onClueToBeAddedClicked(clue: Clue) {
        val listToBeAdded = _state.value.container.cluesToBeAdded.map { existingClue ->
            if (existingClue == clue) {
                existingClue.copy(enabled = !clue.enabled)
            } else {
                existingClue
            }
        }
        _state.update { state ->
            state.copy(container = _state.value.container.copy(cluesToBeAdded = listToBeAdded))
        }
    }
}
