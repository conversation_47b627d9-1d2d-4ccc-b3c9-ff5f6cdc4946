package com.forthosewho.app.stories_home.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.paging.Pager
import androidx.paging.PagingConfig
import androidx.paging.cachedIn
import com.forthosewho.app.account.domain.model.Clue
import com.forthosewho.app.account.domain.usecase.AccountUseCase
import com.forthosewho.app.account.ui.model.ClueView
import com.forthosewho.app.analytics.domain.usecase.AnalyticsUseCase
import com.forthosewho.app.datastore.DataStoreValues
import com.forthosewho.app.datastore.domain.usecase.GetStoredValueUseCase
import com.forthosewho.app.platform.utils.empty
import com.forthosewho.app.stories_home.analytics.NewsfeedEvents
import com.forthosewho.app.stories_home.domain.model.Story
import com.forthosewho.app.stories_home.domain.usecase.GetStoriesUseCase
import com.forthosewho.app.stories_home.ui.model.StoriesListState
import com.forthosewho.app.stories_home.ui.paging.StoriesPagingSource
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.datetime.Clock.System.now
import kotlinx.serialization.json.Json

const val PAGE_SIZE_LIMIT = 25

class StoriesViewModel(
    private val getStoriesUseCase: GetStoriesUseCase,
    private val accountUseCase: AccountUseCase,
    private val config: PagingConfig = PagingConfig(pageSize = PAGE_SIZE_LIMIT),
    private val getStoredValueUseCase: GetStoredValueUseCase = GetStoredValueUseCase(),
    private val analyticsUseCase: AnalyticsUseCase,
) : ViewModel() {

    private val _state = MutableStateFlow(StoriesListState.State())
    private val _lastRefreshTime: MutableStateFlow<Long> = MutableStateFlow(now().epochSeconds)
    private val _pagingDataFlow = setupPeriodicRefresh()
    val state = _state.asStateFlow()

    @OptIn(ExperimentalCoroutinesApi::class)
    private fun setupPeriodicRefresh() = _lastRefreshTime.flatMapLatest { _ ->
        // Start a coroutine for periodic refresh
        viewModelScope.launch {
            delay(5 * 60 * 1000) // 5 minutes in milliseconds
            _lastRefreshTime.value = now().epochSeconds
        }

        Pager(config = config) {
            StoriesPagingSource(useCase = getStoriesUseCase)
        }.flow.cachedIn(viewModelScope).distinctUntilChanged()
    }

    fun initializeViewModel() {
        getUserClues()
        getStories()
    }

    fun refresh(updateUI: Boolean = true) {
        if (!updateUI) {
            _lastRefreshTime.value = now().epochSeconds
        } else {
            _state.update { state -> state.copy(screenState = StoriesListState.ScreenState.StoriesLoading) }
            _lastRefreshTime.value = now().epochSeconds
            viewModelScope.launch {
                delay(700L)
                _state.update { state -> state.copy(screenState = StoriesListState.ScreenState.StoriesLoaded) }
            }
        }
    }

    private fun getStories() {
        viewModelScope.launch {
            _state.update { state ->
                state.copy(
                    screenState = StoriesListState.ScreenState.Initial,
                    stories = _pagingDataFlow.catch { onError(error = it) }.cachedIn(scope = viewModelScope)
                )
            }
        }
    }

    private fun getUserClues() {
        _state.update { state -> state.copy(screenState = StoriesListState.ScreenState.Initial) }
        viewModelScope.launch {
            accountUseCase.invoke()
            val clueList: List<Clue> = Json.decodeFromString(
                getStoredValueUseCase.getString(DataStoreValues.CLUES, String.empty())
            )
            analyticsUseCase.trackEvent(
                NewsfeedEvents.ViewNewsfeed(
                    properties = mapOf("clue_count" to clueList.count().toString())
                )
            )
            _state.update { state ->
                state.copy(
                    userClues = if (clueList.isNotEmpty()) {
                        clueList.map { ClueView.from(it) }
                    } else {
                        listOf()
                    }
                )
            }
        }
    }

    fun selectStory(story: Story) {
        _state.update { state -> state.copy(currentStory = story) }
    }

    fun onError(error: Throwable) {
        _state.update { state -> state.copy(error = error) }
    }

    fun onClueItemClicked(clue: ClueView) {
        _state.update { state ->
            state.copy(screenState = StoriesListState.ScreenState.NavigateToStoriesForClues(clueView = clue))
        }
    }

    fun onBackPressed() {
        _state.update { state -> state.copy(screenState = StoriesListState.ScreenState.NavigateBack) }
    }
}
